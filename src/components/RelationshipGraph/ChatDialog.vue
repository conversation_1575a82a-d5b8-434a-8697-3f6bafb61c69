<template>
  <Transition name="chat-dialog-fade" appear @after-leave="handleAfterLeave">
    <div v-if="visible" class="chat-dialog-overlay" @click="handleOverlayClick">
      <div class="chat-dialog" :class="{ show: visible }" @click.stop>
        <!-- 对话头部 -->
        <div class="chat-header">
          <div class="header-left" @click="handleHeaderClick">
            <div class="header-indicator"></div>
            <div class="header-arrow">
              <div class="arrow-down"></div>
            </div>
          </div>
          <div class="header-right">
            <button v-if="messages.length > 0" class="new-chat-button" title="开始新对话" @click="handleNewChat">
              <span class="plus-icon">+</span>
            </button>
          </div>
        </div>

        <!-- 对话消息区域 -->
        <div ref="chatMessagesRef" class="chat-messages">
          <template v-for="(message, index) in messages" :key="message.key">
            <ChatItem
              :message-data="message"
              :is-regenerate="index === messages.length - 1"
              @regenerate="handleRegenerate"
            />
          </template>

          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="empty-state"></div>
        </div>

        <!-- 输入框区域 -->
        <div
          class="chat-input-area"
          :class="{
            'voice-active': isRecording,
            'keyboard-mode': isKeyboardMode && !isRecording,
          }"
        >
          <!-- 键盘输入模式 -->
          <div v-if="isKeyboardMode && !isRecording" class="keyboard-wrapper">
            <div class="voice-toggle" @click="handleVoiceButtonClick">
              <i class="iconfont icon-microphone" class-prefix="icon"></i>
            </div>
            <div class="input-box">
              <van-field
                v-model="inputMessage"
                class="input-content"
                rows="1"
                autosize
                autocomplete="off"
                inputmode="text"
                type="textarea"
                placeholder="可以问我任何问题"
                @keydown.enter.prevent="handleSendMessage"
                @compositionstart="handleComposition"
                @compositionupdate="handleComposition"
                @compositionend="handleComposition"
                @input="handleInputChange"
              />
            </div>

            <!-- 发送按钮 -->
            <div
              class="send-icon"
              :class="{
                'not-input': inputMessage === '',
              }"
            >
              <i class="iconfont icon-mobile-send" class-prefix="icon" @click="handleSendMessage"></i>
            </div>
          </div>

          <!-- 语音输入模式 -->
          <div v-if="!isKeyboardMode || isRecording">
            <!-- 语音输入时的文字显示区域 -->
            <div v-if="isRecording" class="voice-text-display">
              <div v-if="!voiceMessage" class="voice-placeholder">我在听，请说...</div>
              <div v-else class="voice-message-text">{{ voiceMessage }}</div>
            </div>

            <!-- 按钮区域 -->
            <div class="button-container">
              <!-- 左侧语音输入按钮 -->
              <div class="voice-button" :class="{ recording: isRecording }" @touchstart.prevent="startRecording">
                <div class="voice-button-bg" :class="{ recording: isRecording }"></div>
                <i class="iconfont icon-microphone voice-icon" class-prefix="icon"></i>
              </div>

              <!-- 中间语音动态线 -->
              <div v-if="isRecording" class="voice-wave">
                <div v-for="index in 30" :key="index" class="wave-line"></div>
              </div>

              <!-- 右侧按钮 -->
              <div class="right-button" @click="handleRightButtonClick">
                <span v-if="isRecording" class="cancel-text">取消</span>
                <span v-else class="keyboard-text">键盘</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onBeforeUnmount } from 'vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';
import { showToast, Field as vanField } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';

// Props
interface IProps {
  visible: boolean;
  messages: IChatStreamContent[];
  conversationId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'send-message', message: string): void;
  (e: 'regenerate', messageData: IChatStreamContent): void;
  (e: 'new-chat'): void;
}>();

// Refs
const chatMessagesRef = ref<HTMLElement>();
const inputMessage = ref('');

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
// 保存媒体流引用，用于释放麦克风资源
let mediaStream: MediaStream | null = null;

// 响应式数据
const micPermission = ref(false); // 麦克风权限
const sessionId = ref(''); // 语音转文字sessionId
const audioBufferIndex = ref(0); // 语音转文字流序列号
const lastBuffer = ref(); // 语音转文字最后一条流
const voiceMessage = ref(''); // 发送的对话文字
const isRecording = ref(false); // 是否录音输入
const isKeyboardMode = ref(true); // 是否键盘输入模式
const isOnComposition = ref(false); // 是否在输入法组合状态

// 处理覆盖层点击（点击上半部分收起）
const handleOverlayClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const dialogElement = target.closest('.chat-dialog');

  // 如果点击的不是对话框内部，则关闭
  if (!dialogElement) {
    console.log('🔽 [ChatDialog] 点击覆盖层，收起对话框');
    emit('close');
  }
};

// 处理头部点击（收起功能）
const handleHeaderClick = () => {
  // 清空输入框
  inputMessage.value = '';
  emit('close');
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  emit('regenerate', messageData);
};

// 处理输入变化
const handleInputChange = () => {
  // 可以在这里添加输入验证逻辑
};

// 处理输入法组合事件
const handleComposition = (e: CompositionEvent) => {
  const { type } = e;
  isOnComposition.value = type !== 'compositionend';
};

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  // 直接开始录音，不切换模式
  await startRecording();
};

// 处理右侧按钮点击（键盘切换/取消录音）
const handleRightButtonClick = () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音并释放麦克风资源
    console.log('🚫 [ChatDialog] 用户点击取消录音');
    cancelRecording();
  } else {
    // 切换到键盘输入模式
    isKeyboardMode.value = true;
  }
};

// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    console.log('✅ [ChatDialog] 麦克风权限获取成功');
  } catch (error) {
    console.error('❌ [ChatDialog] 麦克风权限获取失败:', error);
    micPermission.value = false;
    showToast('麦克风权限获取失败，请检查浏览器设置');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const { data: responseData } = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });
        voiceMessage.value = responseData.text;
        void autoSendTimeout();
      } catch (error) {
        console.error('❌ [ChatDialog] 语音识别失败:', error);
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 直接发送语音识别的文字
    handleVoiceSend();
  } else {
    showToast('录音解析为空，请重新录制~');
  }
}

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  // 释放麦克风资源
  releaseMicrophoneResources();
  // 清空语音消息
  voiceMessage.value = '';
  console.log('🚫 [ChatDialog] 录音已取消');
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 处理语音发送
function handleVoiceSend() {
  if (voiceMessage.value === '') {
    return;
  }
  console.log('📤 [ChatDialog] 发送语音消息:', voiceMessage.value);
  emit('send-message', voiceMessage.value);
  voiceMessage.value = '';
}

// 处理发送消息
const handleSendMessage = () => {
  const message = inputMessage.value.trim();
  if (!message) return;

  console.log('📤 [ChatDialog] 发送消息:', message);
  emit('send-message', message);

  // 清空输入框
  inputMessage.value = '';
};

// 处理新对话
const handleNewChat = () => {
  console.log('🆕 [ChatDialog] 开始新对话');
  // 清空输入框
  inputMessage.value = '';
  emit('new-chat');
};

// 处理过渡动画结束后的清理
const handleAfterLeave = () => {
  console.log('🧹 [ChatDialog] 对话框完全关闭，清理状态');
  // 确保输入框被清空
  inputMessage.value = '';
  // 确保body overflow样式被重置
  document.body.style.overflow = '';
  // 强制重新渲染，确保DOM完全清理
  void nextTick(() => {
    console.log('✅ [ChatDialog] DOM清理完成');
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTo({
      top: chatMessagesRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    void nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true },
);

// 监听显示状态变化，显示时滚动到底部
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      void nextTick(() => {
        scrollToBottom();
      });
    } else {
      // 对话框关闭时清空输入框
      inputMessage.value = '';
      // 清空语音消息
      voiceMessage.value = '';
      // 如果正在录音，停止录音
      if (isRecording.value) {
        cancelRecording();
      }
      // 确保body overflow样式被重置
      document.body.style.overflow = '';
    }
  },
);

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    cancelRecording();
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);
}

.chat-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
  pointer-events: auto;
}

.chat-dialog {
  width: 100%;
  max-width: 100vw;
  height: 50vh;
  background: rgba(1, 28, 32, 0.9);
  border: 2px solid var(--border-accent);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out forwards;

  &.show {
    animation: slideUp 0.3s ease-out forwards;
  }

  &.hide {
    animation: slideDown 0.3s ease-in forwards;
  }
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-glass);
  display: flex;
  align-items: center;
  position: relative;
  min-height: 100px;

  .header-left {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: 100%;
    position: relative;

    .header-indicator {
      width: 40px;
      height: 4px;
      background: var(--accent-color);
      border-radius: 2px;
      position: absolute;
      left: 50%;
      top: -8px;
      transform: translateX(-50%);
    }

    .header-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;

      .arrow-down {
        width: 0;
        height: 0;
        border-left: 14px solid transparent;
        border-right: 14px solid transparent;
        border-top: 18px solid var(--text-primary);
        transition: transform 0.2s ease;
      }
    }
  }

  .header-right {
    position: absolute;
    top: 24px;
    right: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    .new-chat-button {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 4px solid var(--border-accent);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(20px);

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--accent-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
      }

      .plus-icon {
        color: #ffffff;
        font-size: 24px;
        font-weight: 600;
        line-height: 1;
        margin-bottom: 3px;
      }
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 400;
  }
}

.chat-input-area {
  width: 100%;
  min-height: 170px;
  background: transparent;
  border-radius: 20px 20px 0px 0px;
  padding: 28px 40px;
  box-sizing: border-box;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 36px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 36px);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  transition: all 0.3s ease;
  position: relative;

  // 录音状态下恢复原来的高度和背景效果
  &.voice-active {
    min-height: 300px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0.3) 100%);
  }

  &.keyboard-mode {
    min-height: auto;
    background: var(--bg-glass);
    border: 2px solid var(--border-glass);
    backdrop-filter: blur(20px);
    padding: 16px 23px;
    box-shadow: var(--shadow-strong);

    // iOS Safari 兼容性修复
    @supports (-webkit-touch-callout: none) {
      background: var(--bg-glass) !important;
      border-color: var(--border-glass) !important;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }
  }

  // 键盘输入模式样式
  .keyboard-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 16px;
    width: 100%;

    .voice-toggle {
      width: 88px;
      height: 88px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong), var(--shadow-accent);

      // iOS Safari 兼容性修复
      @supports (-webkit-touch-callout: none) {
        background: var(--bg-glass) !important;
        border-color: var(--border-accent) !important;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
      }

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--accent-color);
        transform: translateY(-2px);
        box-shadow:
          var(--shadow-strong),
          0 8px 24px var(--accent-color-strong);
      }

      i {
        color: var(--accent-color);
        font-size: 56px;
      }
    }

    .input-box {
      flex: 1;
      display: flex;
      align-items: stretch;
      margin-left: 20px;
      margin-right: 16px;

      .input-content {
        width: 100%;
        max-height: 192px;
        min-height: 88px;
        padding: 24px 20px !important;
        box-sizing: border-box;
        border-radius: var(--border-radius-xl);
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        box-shadow: var(--shadow-medium);

        // iOS Safari 兼容性修复
        @supports (-webkit-touch-callout: none) {
          background: var(--bg-glass) !important;
          border-color: var(--border-accent) !important;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        &:focus-within {
          border-color: var(--accent-color);
          box-shadow:
            0 0 0 3px var(--accent-color-light),
            var(--shadow-strong);
          transform: translateY(-2px);
        }

        :deep(.van-field__control) {
          color: var(--text-primary);
          font-size: var(--font-size-2xl) !important;
          line-height: 1.4;
          max-height: 165px;
          font-weight: 500;
        }

        :deep(.van-field__control::placeholder) {
          color: var(--text-quaternary);
          font-size: var(--font-size-xl) !important;
          font-weight: 400;
        }
      }
    }

    .send-icon {
      width: 88px;
      height: 88px;
      border-radius: 50%;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      flex-shrink: 0;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong);

      &:hover:not(.not-input) {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow:
          var(--shadow-strong),
          0 8px 24px var(--primary-color-strong);
      }

      &.not-input {
        background: var(--text-disabled);
        border-color: var(--text-disabled);
        cursor: not-allowed;
        opacity: 0.5;
      }

      i {
        color: var(--primary-color);
        font-size: 56px;
      }
    }
  }

  // 语音输入模式样式
  .voice-text-display {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--bg-glass);
    border: 2px solid var(--border-accent);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-strong), var(--shadow-accent);
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--text-tertiary);
      font-size: calc(var(--font-size-xl) + 4px);
      font-weight: 400;
      text-align: center;
    }

    .voice-message-text {
      color: var(--text-primary);
      font-size: calc(var(--font-size-2xl) + 4px);
      font-weight: 500;
      line-height: 1.4;
      text-align: center;
    }
  }

  .button-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .voice-button {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .voice-button-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        box-shadow: var(--shadow-strong), var(--shadow-accent);
        transition: all 0.3s ease;

        &.recording {
          background: var(--accent-color-light);
          border-color: var(--accent-color);
          box-shadow:
            var(--shadow-strong),
            0 0 30px var(--accent-color-strong),
            0 0 60px var(--accent-color-medium);
          animation: pulse 1.5s ease-in-out infinite;
        }
      }

      .voice-icon {
        color: var(--accent-color);
        font-size: 64px;
        z-index: 1;
        transition: all 0.3s ease;
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .voice-wave {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 3px;
      height: 60px;
      margin: 0 20px;

      .wave-line {
        width: 4px;
        background: var(--accent-color);
        border-radius: 2px;
        animation: wave 1.5s ease-in-out infinite;
        opacity: 0.7;

        &:nth-child(odd) {
          animation-delay: 0.1s;
        }

        &:nth-child(even) {
          animation-delay: 0.3s;
        }

        &:nth-child(3n) {
          animation-delay: 0.5s;
        }
      }
    }

    .right-button {
      width: 120px;
      height: 60px;
      border-radius: 30px;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong);

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--accent-color);
        transform: translateY(-2px);
      }

      .cancel-text,
      .keyboard-text {
        color: var(--text-primary);
        font-size: calc(var(--font-size-lg) + 8px);
        font-weight: 500;
      }

      .cancel-text {
        color: var(--error-color);
      }
    }
  }
}

// Transition 动画
.chat-dialog-fade-enter-active,
.chat-dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.chat-dialog-fade-enter-from,
.chat-dialog-fade-leave-to {
  opacity: 0;
}

// 确保离开动画完成后元素完全移除
.chat-dialog-fade-leave-to {
  pointer-events: none;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes wave {
  0%,
  100% {
    height: 20px;
  }
  50% {
    height: 60px;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .chat-dialog {
    height: 60vh;
    border-radius: 16px 16px 0 0;
  }

  .chat-header {
    padding: 12px 16px;

    .header-title {
      font-size: 16px;
    }
  }

  .chat-messages {
    padding: 16px;
    gap: 12px;
  }

  .chat-input-area {
    padding: 20px 20px;
    min-height: 120px;

    &.keyboard-mode {
      padding: 16px 20px;

      .keyboard-wrapper {
        gap: 12px;

        .voice-toggle {
          width: 76px;
          height: 76px;

          i {
            font-size: 48px;
          }
        }

        .input-box {
          margin-left: 12px;
          margin-right: 12px;

          .input-content {
            min-height: 76px;
            padding: 20px 16px !important;
            border-radius: var(--border-radius-lg);

            :deep(.van-field__control) {
              font-size: var(--font-size-xl) !important;
              line-height: 1.4;
            }

            :deep(.van-field__control::placeholder) {
              font-size: 24px;
            }
          }
        }

        .send-icon {
          width: 76px;
          height: 76px;

          i {
            font-size: 48px;
          }
        }
      }
    }

    .button-container {
      gap: 16px;

      .voice-button {
        width: 100px;
        height: 100px;

        .voice-icon {
          font-size: 56px;
        }
      }

      .right-button {
        width: 100px;
        height: 50px;

        .cancel-text,
        .keyboard-text {
          font-size: calc(var(--font-size-md) + 8px);
        }
      }
    }

    .voice-text-display {
      margin-bottom: 16px;
      padding: 16px;
      min-height: 50px;

      .voice-placeholder {
        font-size: calc(var(--font-size-lg) + 8px);
      }

      .voice-message-text {
        font-size: calc(var(--font-size-xl) + 8px);
      }
    }
  }
}
</style>
