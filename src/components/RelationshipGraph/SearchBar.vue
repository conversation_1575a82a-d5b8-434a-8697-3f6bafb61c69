<template>
  <div class="search-container">
    <div class="search-input-wrapper">
      <input
        v-model="searchQuery"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        maxlength="50"
        @keyup.enter="handleSearch"
        @input="onSearchInput"
      />
      <button class="search-confirm-btn" :disabled="!searchQuery.trim() || isSearching" @click="handleSearch">
        <template v-if="isSearching">
          <img src="@/assets/icon/searching.png" alt="搜索中" />
        </template>
        <template v-else>
          <img src="@/assets/icon/search.png" alt="搜索" />
        </template>
      </button>
      <button class="add-person-btn-header" @click="handleAdd">
        <span class="add-icon">+</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

// Props
interface IProps {
  placeholder?: string;
  isSearching?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  placeholder: '请输入姓名进行搜索',
  isSearching: false,
});

// Emits
const emit = defineEmits<{
  search: [query: string];
  add: [];
  input: [query: string];
}>();

// 响应式数据
const searchQuery = ref('');

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim());
  }
};

// 处理添加
const handleAdd = () => {
  emit('add');
};

// 处理输入
const onSearchInput = () => {
  emit('input', searchQuery.value);
};

// 监听外部搜索状态变化，重置搜索框
watch(
  () => props.isSearching,
  (newVal) => {
    if (!newVal) {
      // 搜索完成后可以选择是否清空搜索框
      // searchQuery.value = '';
    }
  },
);

// 暴露方法供父组件调用
defineExpose({
  clearSearch: () => {
    searchQuery.value = '';
  },
  getSearchQuery: () => searchQuery.value,
});
</script>

<style lang="scss" scoped>
.search-container {
  padding: 18px 0px;

  .search-input-wrapper {
    display: flex;
    gap: 20px;
    align-items: center;

    .search-input {
      flex: 1;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 18px;
      padding: 16px 20px;
      color: rgba(255, 255, 255, 0.95);
      font-size: 26px; // 增大字体，原来是22px
      box-sizing: border-box;
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
      }
    }

    .search-confirm-btn {
      padding: 0;
      border-radius: 50%;
      font-size: 30px;
      font-weight: 1000;
      cursor: pointer;
      border: 2px solid;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      width: 58px;
      height: 58px; // 与search-input高度一致，设置相同宽高形成正圆
      box-sizing: border-box;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .add-person-btn-header {
      padding: 16px 16px;
      border-radius: 50%;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      min-width: 60px;
      height: 58px; // 与search-input高度一致
      box-sizing: border-box;

      .add-icon {
        font-size: 32px;
        line-height: 1;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .search-confirm-btn {
      color: #00bcd4;
      border-color: #00bcd4;
      background: rgba(0, 188, 212, 0.15);

      img {
        width: 28px;
        height: 28px;
      }
    }

    .add-person-btn-header {
      color: #00bcd4;
      border-color: #00bcd4;
      background: rgba(0, 188, 212, 0.15);
    }
  }
}
</style>
